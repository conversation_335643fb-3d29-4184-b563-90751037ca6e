'use client'

import { useEffect, useState } from 'react'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Button } from '@/components/ui/Button'
import { formatDate } from '@/lib/utils'
import { TaskStatus, TaskPriority } from 'generated-prisma'

interface TaskStats {
  totalTasks: number
  pendingTasks: number
  processingTasks: number
  completedTasks: number
  failedTasks: number
}

interface Task {
  id: number
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  createdAt: string
  updatedAt: string
  completedAt?: string
  lastError?: string
  lastErrorTime?: string
  mailId?: string
  project?: {
    id: number
    projectCode: string
    projectName: string
  }
  assignedTo?: {
    id: number
    name: string
    email: string
  }
  qcTo?: {
    id: number
    name: string
    email: string
  }
  deTo?: {
    id: number
    name: string
    email: string
  }
  createdBy: {
    id: number
    name: string
    email: string
  }
}

interface TaskListResponse {
  success: boolean
  tasks: Task[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

interface ProcessingData {
  processingStats: TaskStats
  isCurrentlyProcessing: boolean
}

export default function TasksPage() {
  const [tasks, setTasks] = useState<Task[]>([])
  const [processingData, setProcessingData] = useState<ProcessingData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)

  // 筛选和分页状态
  const [currentPage, setCurrentPage] = useState(1)
  const [statusFilter, setStatusFilter] = useState<TaskStatus | ''>('')
  const [projectFilter, setProjectFilter] = useState('')
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  })

  useEffect(() => {
    loadTasks()
    loadProcessingData()
  }, [currentPage, statusFilter, projectFilter])

  const loadTasks = async () => {
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10'
      })

      if (statusFilter) {
        params.append('status', statusFilter)
      }
      if (projectFilter) {
        params.append('projectId', projectFilter)
      }

      const response = await fetch(`/api/tasks?${params}`, {
        credentials: 'include'
      })

      const result: TaskListResponse = await response.json()

      if (result.success) {
        setTasks(result.tasks)
        setPagination(result.pagination)
        setError('')
      } else {
        setError('获取任务列表失败')
      }
    } catch (error) {
      console.error('Load tasks failed:', error)
      setError('获取任务列表失败')
    } finally {
      setIsLoading(false)
    }
  }

  const loadProcessingData = async () => {
    try {
      const response = await fetch('/api/jobs/process-tasks', {
        credentials: 'include'
      })

      const result = await response.json()

      if (result.success) {
        setProcessingData(result.data)
      }
    } catch (error) {
      console.error('Load processing data failed:', error)
    }
  }

  const handleProcessTasks = async () => {
    if (processingData?.isCurrentlyProcessing) {
      alert('任务处理正在进行中，请稍后再试')
      return
    }

    if (!confirm('确定要手动触发任务处理吗？')) {
      return
    }

    try {
      setIsProcessing(true)
      const response = await fetch('/api/jobs/process-tasks', {
        method: 'POST',
        credentials: 'include'
      })

      const result = await response.json()

      if (result.success) {
        alert(`任务处理完成！\n成功处理: ${result.data.processedCount} 个任务\n失败: ${result.data.failedCount} 个任务`)
        loadTasks() // 重新加载任务列表
        loadProcessingData() // 重新加载处理状态
      } else {
        alert(`任务处理失败: ${result.error || '未知错误'}`)
      }
    } catch (error) {
      console.error('Process tasks failed:', error)
      alert('任务处理失败')
    } finally {
      setIsProcessing(false)
    }
  }

  const getStatusText = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return '待处理'
      case TaskStatus.IN_PROGRESS:
        return '处理中'
      case TaskStatus.COMPLETED:
        return '已完成'
      case TaskStatus.CANCELLED:
        return '已取消'
      default:
        return status
    }
  }

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800'
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800'
      case TaskStatus.COMPLETED:
        return 'bg-green-100 text-green-800'
      case TaskStatus.CANCELLED:
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityText = (priority: TaskPriority) => {
    switch (priority) {
      case TaskPriority.LOW:
        return '低'
      case TaskPriority.MEDIUM:
        return '中'
      case TaskPriority.HIGH:
        return '高'
      case TaskPriority.URGENT:
        return '紧急'
      default:
        return priority
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">任务管理</h1>
          <p className="mt-1 text-sm text-gray-500">
            查看和管理系统任务
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => {
              loadTasks()
              loadProcessingData()
            }}
            disabled={isLoading}
          >
            刷新数据
          </Button>
          <Button
            onClick={handleProcessTasks}
            disabled={isProcessing || processingData?.isCurrentlyProcessing}
          >
            {isProcessing || processingData?.isCurrentlyProcessing ? '处理中...' : '手动处理任务'}
          </Button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {/* 任务统计卡片 */}
      {processingData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">总</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      总任务数
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {processingData.processingStats.totalTasks}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">待</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      待处理
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {processingData.processingStats.pendingTasks}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">进</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      处理中
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {processingData.processingStats.processingTasks}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">完</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      已完成
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {processingData.processingStats.completedTasks}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-medium">失</span>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      失败
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {processingData.processingStats.failedTasks}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 调度器状态和统计信息 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 调度器状态 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              调度器状态
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">运行状态</span>
                <StatusIndicator
                  isActive={data.isCurrentlyProcessing}
                  activeText="处理中"
                  inactiveText="空闲"
                />
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">总执行次数</span>
                <span className="text-sm font-medium text-gray-900">
                  {data.taskProcessorStatus.totalExecutions}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">成功次数</span>
                <span className="text-sm font-medium text-green-600">
                  {data.taskProcessorStatus.successfulExecutions}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">失败次数</span>
                <span className="text-sm font-medium text-red-600">
                  {data.taskProcessorStatus.failedExecutions}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">成功率</span>
                <span className="text-sm font-medium text-gray-900">
                  {successRate}%
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">最后执行时间</span>
                <span className="text-sm font-medium text-gray-900">
                  {data.taskProcessorStatus.lastExecutionTime 
                    ? formatDate(data.taskProcessorStatus.lastExecutionTime)
                    : '从未执行'
                  }
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 性能统计 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              性能统计
            </h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">平均处理时间</span>
                <span className="text-sm font-medium text-gray-900">
                  {formatDuration(data.statistics.averageProcessingTime)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">总处理任务数</span>
                <span className="text-sm font-medium text-gray-900">
                  {data.statistics.totalTasksProcessed}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">总失败任务数</span>
                <span className="text-sm font-medium text-red-600">
                  {data.statistics.totalTasksFailed}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-500">系统运行时间</span>
                <span className="text-sm font-medium text-gray-900">
                  {formatDuration(data.statistics.uptime)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 最近执行状态 */}
      {data.lastExecutionStatus && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              最近执行状态
            </h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <span className="text-sm text-gray-500">执行ID</span>
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {data.lastExecutionStatus.id}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">状态</span>
                  <p className={`text-sm font-medium ${
                    data.lastExecutionStatus.status === 'completed'
                      ? 'text-green-600'
                      : data.lastExecutionStatus.status === 'failed'
                      ? 'text-red-600'
                      : 'text-blue-600'
                  }`}>
                    {data.lastExecutionStatus.status === 'completed' ? '已完成' :
                     data.lastExecutionStatus.status === 'failed' ? '失败' : '运行中'}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">处理任务</span>
                  <p className="text-sm font-medium text-gray-900">
                    成功: {data.lastExecutionStatus.processedCount} / 失败: {data.lastExecutionStatus.failedCount}
                  </p>
                </div>
                <div>
                  <span className="text-sm text-gray-500">执行时间</span>
                  <p className="text-sm font-medium text-gray-900">
                    {data.lastExecutionStatus.duration ? formatDuration(data.lastExecutionStatus.duration) : '-'}
                  </p>
                </div>
              </div>
              {data.lastExecutionStatus.error && (
                <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">
                    <strong>错误信息:</strong> {data.lastExecutionStatus.error}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 执行日志 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            执行日志 (最近10条)
          </h3>
          {data.executionLogs.length > 0 ? (
            <div className="overflow-hidden">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      执行时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      任务处理
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      耗时
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      触发者
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {data.executionLogs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(log.startTime)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          log.status === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : log.status === 'failed'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {log.status === 'completed' ? '已完成' :
                           log.status === 'failed' ? '失败' : '运行中'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex space-x-2">
                          <span className="text-green-600">✓ {log.processedCount}</span>
                          <span className="text-red-600">✗ {log.failedCount}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {log.duration ? formatDuration(log.duration) : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.triggeredBy || '系统'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleViewExecution(log)}
                        >
                          查看详情
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无执行日志</h3>
              <p className="mt-1 text-sm text-gray-500">
                还没有任何任务处理记录
              </p>
            </div>
          )}
        </div>
      </div>

      {/* 执行详情模态框 */}
      <TaskExecutionModal
        isOpen={showExecutionModal}
        onClose={() => setShowExecutionModal(false)}
        execution={selectedExecution}
      />
    </div>
  )
}
