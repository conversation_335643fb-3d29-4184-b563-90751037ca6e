import { NextRequest, NextResponse } from 'next/server'
import { taskProcessorService } from '@/lib/services/taskProcessorService'
import { requireAuth } from '@/lib/auth'
import { UserRole } from 'generated-prisma'
import logger, { apiLog } from '@/lib/logger'

/**
 * 手动触发任务处理
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now()
  apiLog.request('POST', '/api/jobs/process-tasks')

  try {
    // 验证用户权限 - 只有WM和PM角色可以触发
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM])
    if (!authResult.success) {
      apiLog.response('POST', '/api/jobs/process-tasks', authResult.status, Date.now() - startTime, {
        error: 'Authentication failed'
      })
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    logger.info('用户触发任务处理', {
      userId: authResult.user?.id,
      userName: authResult.user?.name,
      action: 'manual_task_trigger'
    })

    // 检查是否已有任务在处理
    if (taskProcessorService.isCurrentlyProcessing()) {
      logger.warn('任务处理已在进行中，拒绝重复请求', {
        userId: authResult.user?.id,
        action: 'task_processing_busy'
      })
      apiLog.response('POST', '/api/jobs/process-tasks', 409, Date.now() - startTime, {
        message: 'Task processing already in progress'
      })
      return NextResponse.json({
        success: false,
        message: '任务处理正在进行中，请稍后再试',
        isProcessing: true
      })
    }

    // 执行任务处理
    logger.info('开始执行任务处理', {
      userId: authResult.user?.id,
      action: 'task_processing_start'
    })

    const processingResult = await taskProcessorService.processAllPendingTasks()

    logger.info('任务处理执行完成', {
      userId: authResult.user?.id,
      processedCount: processingResult.processedCount,
      failedCount: processingResult.failedCount,
      success: processingResult.success,
      action: 'task_processing_complete'
    })

    apiLog.response('POST', '/api/jobs/process-tasks', 200, Date.now() - startTime, {
      processedCount: processingResult.processedCount,
      failedCount: processingResult.failedCount,
      success: processingResult.success
    })

    return NextResponse.json({
      success: processingResult.success,
      message: processingResult.success ? '任务处理完成' : '任务处理失败',
      data: {
        processedCount: processingResult.processedCount,
        failedCount: processingResult.failedCount,
        success: processingResult.success,
        results: processingResult.results
      }
    })

  } catch (error) {
    apiLog.error('POST', '/api/jobs/process-tasks', error as Error, {
      duration: Date.now() - startTime
    })
    return NextResponse.json(
      {
        success: false,
        error: '任务处理失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

/**
 * 获取任务处理状态和统计信息
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now()
  apiLog.request('GET', '/api/jobs/process-tasks')

  try {
    // 验证用户权限
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM, UserRole.Triage])
    if (!authResult.success) {
      apiLog.response('GET', '/api/jobs/process-tasks', authResult.status, Date.now() - startTime, {
        error: 'Authentication failed'
      })
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    logger.debug('获取任务处理状态', {
      userId: authResult.user?.id,
      action: 'get_task_status'
    })

    // 获取处理统计信息
    const processingStats = await taskProcessorService.getProcessingStats()

    apiLog.response('GET', '/api/jobs/process-tasks', 200, Date.now() - startTime, {
      totalTasks: processingStats.totalTasks,
      pendingTasks: processingStats.pendingTasks,
      isProcessing: taskProcessorService.isCurrentlyProcessing()
    })

    return NextResponse.json({
      success: true,
      data: {
        processingStats,
        isCurrentlyProcessing: taskProcessorService.isCurrentlyProcessing()
      }
    })

  } catch (error) {
    apiLog.error('GET', '/api/jobs/process-tasks', error as Error, {
      duration: Date.now() - startTime
    })
    return NextResponse.json(
      {
        success: false,
        error: '获取状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
